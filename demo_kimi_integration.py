#!/usr/bin/env python3
"""
Demo script showing Kimi K2 integration with fallback to Gemini
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
import google.generativeai as genai

# Load environment variables
load_dotenv()

def demo_grading_system():
    """Demonstrate the grading system with Kimi K2 and Gemini fallback"""
    
    print("🎯 Grading System Integration Demo")
    print("=" * 50)
    
    # Check Kimi K2 availability
    kimi_api_key = os.getenv("KIMI_API_KEY")
    kimi_client = None
    
    if kimi_api_key:
        try:
            kimi_client = OpenAI(
                api_key=kimi_api_key,
                base_url="https://api.moonshot.ai/v1"
            )
            print("✅ Kimi K2 client initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Kimi K2 client: {e}")
            kimi_client = None
    else:
        print("⚠️ KIMI_API_KEY not found - will use Gemini fallback")
    
    # Initialize Gemini as fallback
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    gemini_client = None
    
    if gemini_api_key:
        try:
            genai.configure(api_key=gemini_api_key)
            gemini_client = genai.GenerativeModel('gemini-2.5-flash')
            print("✅ Gemini client initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize Gemini client: {e}")
    else:
        print("❌ GEMINI_API_KEY not found")
    
    print("\n" + "=" * 50)
    
    # Demo grading prompt
    sample_prompt = """You are an expert examiner evaluating a student's answer against a specific marking point.

RESPONSE FORMAT:
You must respond in one of these 3 formats ONLY:

Format 1 - If the marking point is FULLY addressed:
YES
EVIDENCE: <exact text snippet>

Format 2 - If the marking point was PARTIALLY addressed:
PARTIAL
EVIDENCE: <exact text snippet>

Format 3 - If the marking point is NOT addressed:
NO

MARKING POINT: Explain the concept of gravity
STUDENT'S ANSWER: Gravity is a force that pulls objects toward the center of the Earth. It's what makes things fall down when you drop them."""

    # Test grading with available client
    if kimi_client:
        print("\n🚀 Testing with Kimi K2...")
        try:
            completion = kimi_client.chat.completions.create(
                model="kimi-k2-0711-preview",
                messages=[
                    {"role": "system", "content": "You are Kimi, an AI assistant provided by Moonshot AI. You are proficient in Chinese and English conversations. You provide users with safe, helpful, and accurate answers. You will reject any questions involving terrorism, racism, or explicit content. Moonshot AI is a proper noun and should not be translated."},
                    {"role": "user", "content": sample_prompt}
                ],
                temperature=0.1,
            )
            
            response = completion.choices[0].message.content
            print(f"✅ Kimi K2 Response:\n{response}")
            
        except Exception as e:
            print(f"❌ Kimi K2 failed: {e}")
            print("🔄 Falling back to Gemini...")
            kimi_client = None  # Force fallback
    
    if not kimi_client and gemini_client:
        print("\n🔄 Using Gemini fallback...")
        try:
            response = gemini_client.generate_content(
                sample_prompt,
                generation_config={"temperature": 0.1}
            )
            
            print(f"✅ Gemini Response:\n{response.text}")
            
        except Exception as e:
            print(f"❌ Gemini also failed: {e}")
    
    print("\n" + "=" * 50)
    print("📋 Integration Summary:")
    print(f"• Kimi K2 Available: {'✅ Yes' if kimi_client else '❌ No'}")
    print(f"• Gemini Fallback: {'✅ Yes' if gemini_client else '❌ No'}")
    
    if kimi_client:
        print("• System will use Kimi K2 for grading")
    elif gemini_client:
        print("• System will use Gemini fallback for grading")
    else:
        print("• ⚠️ No grading client available!")
    
    print("\n🔧 To enable Kimi K2:")
    print("1. Add KIMI_API_KEY to your .env file")
    print("2. Restart the application")
    print("3. The system will automatically use Kimi K2 when available")

if __name__ == "__main__":
    demo_grading_system()
