# Kimi K2 Grading Integration

This document explains how to use the Kimi K2 model for grading in the VAST system.

## Overview

The system has been updated to support Kimi K2 as the primary grading model, with automatic fallback to <PERSON> when Kimi K2 is not available.

## Features

- **Primary Grading**: Uses Kimi K2 model (`kimi-k2-0711-preview`) for evaluating student answers
- **Automatic Fallback**: Falls back to Gemini when Kimi K2 is unavailable
- **Same Interface**: No changes to existing grading endpoints or functionality
- **Parallel Processing**: Maintains parallel processing of marking points for performance

## Setup

### 1. Add API Key

Add your Kimi API key to the `.env` file:

```bash
KIMI_API_KEY=your_kimi_api_key_here
```

### 2. Restart Application

Restart the Flask application to load the new configuration:

```bash
python app.py
```

### 3. Verify Integration

Run the demo script to verify the integration:

```bash
python3 demo_kimi_integration.py
```

## How It Works

### Grading Process

1. **Client Initialization**: The system attempts to initialize the Kimi K2 client on startup
2. **Grading Request**: When a grading request is made, the system checks if Kimi K2 is available
3. **Model Selection**: 
   - If Kimi K2 is available → Uses Kimi K2
   - If Kimi K2 is unavailable → Falls back to Gemini
4. **Response Processing**: Processes the response using the same format for both models

### Kimi K2 Configuration

The Kimi K2 client uses the following configuration:

```python
completion = client.chat.completions.create(
    model="kimi-k2-0711-preview",
    messages=[
        {"role": "system", "content": "You are Kimi, an AI assistant provided by Moonshot AI. You are proficient in Chinese and English conversations. You provide users with safe, helpful, and accurate answers. You will reject any questions involving terrorism, racism, or explicit content. Moonshot AI is a proper noun and should not be translated."},
        {"role": "user", "content": grading_prompt}
    ],
    temperature=0.1,
)
```

## Modified Files

### `app.py`
- Added Kimi client initialization with environment variable support
- Added fallback logic when API key is not available

### `routes/api.py`
- Added `_process_single_marking_point_kimi()` function
- Added `_calculate_score_and_evaluated_points_kimi()` function
- Updated all grading endpoints to use Kimi K2 with Gemini fallback

## Grading Endpoints

The following endpoints now use Kimi K2 (with Gemini fallback):

1. **Submit Answer**: `/submit_answer/<question_id>/<part_id>`
2. **Get Diff**: `/get_diff/<question_id>/<part_id>`
3. **Highlighted Answer**: `/highlighted_answer/<question_id>/<part_id>`
4. **Submit Problemset**: `/submit_problemset/<problemset_id>`

## Testing

### Test Scripts

1. **Basic Integration Test**: `test_kimi_grading.py`
2. **Demo with Fallback**: `demo_kimi_integration.py`

### Running Tests

```bash
# Test Kimi K2 connection (requires API key)
python3 test_kimi_grading.py

# Demo the integration with fallback
python3 demo_kimi_integration.py
```

## Monitoring

The system logs which model is being used for each grading request:

- `"Using Kimi K2 for grading"` - When Kimi K2 is used
- `"Falling back to Gemini for grading (Kimi K2 not available)"` - When fallback occurs

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Ensure `KIMI_API_KEY` is in your `.env` file
   - Restart the application after adding the key

2. **Quota Exceeded**
   - Check your Kimi API quota and billing
   - System will automatically fall back to Gemini

3. **Connection Issues**
   - Verify internet connectivity
   - Check if the Kimi API endpoint is accessible

### Logs

Check the application logs for grading model selection:

```bash
tail -f logs/app.log | grep -E "(Kimi|Gemini|grading)"
```

## Performance

- **Parallel Processing**: Both Kimi K2 and Gemini use parallel processing for multiple marking points
- **Response Time**: Kimi K2 response times may vary; fallback ensures system reliability
- **Caching**: Consider implementing response caching for frequently graded content

## Future Enhancements

- **Model Selection UI**: Admin interface to choose between models
- **A/B Testing**: Compare grading quality between models
- **Response Caching**: Cache grading responses for identical answers
- **Batch Processing**: Process multiple submissions in batches
